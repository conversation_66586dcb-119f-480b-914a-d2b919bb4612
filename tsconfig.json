{"compilerOptions": {"target": "esnext", "lib": ["es2017"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx"}, "include": ["src/**/*", "App.tsx", "index.js"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}